import utils, { tools } from './utils.js'

process.stdin.on('data', data => {
  const req = JSON.parse(data)
  let result
  if(req.method === 'tools/call') {
    return tools[req.params.name](req.params.arguments)
  } else if(req.method in utils) {
    result = utils[req.method](req.params)
  } else {
    return
  }
  const res = {
    id: req.id,
    result,
    jsonrpc: '2.0'
  }
  process.stdout.write(JSON.stringify(res) + '\n')
})