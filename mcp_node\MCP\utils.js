import fs from 'fs'
// 导出工具
export const tools = {
  // 提供两个服务
  sum({a, b}) {
    return {
      content: [
        {
          type: 'text',
          text: `两数求和的结果为：${a + b}`
        }
      ]
    }
  },
  
  createFile({filename, content}) {
    try {
      fs.writeFileSync(filename, content)
      return {
        content: [
          {
            type: 'text',
            text: '文件创建成功'
          }
        ]
      }
    } catch (err) {
      return {
        content: [
          {
            type: 'text',
            text: '文件创建失败'
          }
        ]
      }
    }
  }
}

export default {
  initialize() {
    return {
      content: [
        {
          type: 'text',
          text: '初始化成功'
        }
      ]
    }
  }
}