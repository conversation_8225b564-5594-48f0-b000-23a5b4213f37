import { spawn } from 'child_process'; // 子进程 

// 启动 server.js  子进程 spawn用于创建子进程运行其他程序

const serverProcess = spawn('node', ['server.js'])

// 监听子进程的标准输出，当服务端有数据输出时，会触发回调函数
serverProcess.stdout.on('data', data => {
  console.log('服务端响应:', data.toString());
})

// 发送几条消息
const messages = ['你好', '生命有意义吗', '再见']

messages.forEach((msg, index) => {
  setTimeout(() => {
    // 通过子进程的标准输入将消息发送给服务端
    serverProcess.stdin.write(msg + '\n');
  }, index * 1000);
})

